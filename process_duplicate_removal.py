#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理2目录中的Excel文件，删除重复标题并保存到2-已处理目录
"""

import pandas as pd
import os
import glob
from datetime import datetime

def load_existing_titles(master_file):
    """加载已有的标题数据"""
    try:
        df = pd.read_excel(master_file)
        existing_titles = set(df['标题'].dropna().tolist())
        print(f"已加载 {len(existing_titles)} 个已有标题")
        return existing_titles, df
    except Exception as e:
        print(f"加载主文件出错: {str(e)}")
        return set(), pd.DataFrame()

def process_single_file(file_path, existing_titles, output_dir):
    """处理单个Excel文件"""
    try:
        print(f"\n正在处理文件: {os.path.basename(file_path)}")
        
        # 读取文件（以第2行作为列标题）
        df = pd.read_excel(file_path, header=1)
        
        if '标题' not in df.columns:
            print(f"警告: 文件中未找到'标题'列")
            return 0, []
        
        original_count = len(df)
        print(f"原始数据行数: {original_count}")
        
        # 找出重复的标题行
        df['is_duplicate'] = df['标题'].isin(existing_titles)
        duplicate_count = df['is_duplicate'].sum()
        
        # 删除重复行
        df_filtered = df[~df['is_duplicate']].copy()
        df_filtered = df_filtered.drop('is_duplicate', axis=1)
        
        remaining_count = len(df_filtered)
        removed_count = original_count - remaining_count
        
        print(f"删除重复行数: {removed_count}")
        print(f"保留数据行数: {remaining_count}")
        
        # 获取新的标题（用于更新主文件）
        new_titles = df_filtered['标题'].dropna().tolist()
        
        # 保存处理后的文件
        if remaining_count > 0:
            output_filename = os.path.join(output_dir, os.path.basename(file_path))
            df_filtered.to_excel(output_filename, index=False, engine='openpyxl')
            print(f"已保存处理后文件: {output_filename}")
        else:
            print("文件中所有数据都是重复的，不保存空文件")
        
        return remaining_count, new_titles
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return 0, []

def update_master_file(master_file, new_titles):
    """更新主标题文件"""
    try:
        if not new_titles:
            return
        
        # 读取现有数据
        df_master = pd.read_excel(master_file)
        current_max_id = df_master['序号'].max()
        
        # 创建新数据
        new_data = pd.DataFrame({
            '序号': range(current_max_id + 1, current_max_id + 1 + len(new_titles)),
            '标题': new_titles
        })
        
        # 合并数据
        df_updated = pd.concat([df_master, new_data], ignore_index=True)
        
        # 保存更新后的文件
        df_updated.to_excel(master_file, index=False, engine='openpyxl')
        print(f"已向主文件添加 {len(new_titles)} 个新标题")
        
    except Exception as e:
        print(f"更新主文件时出错: {str(e)}")

def main():
    """主函数"""
    print("开始处理2目录中的Excel文件...")
    
    # 设置路径
    source_dir = "2"
    output_dir = "2-已处理"
    master_file = "合并标题数据_20250629_084039.xlsx"
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: {output_dir}")
    
    # 检查主文件是否存在
    if not os.path.exists(master_file):
        print(f"错误: 主文件 {master_file} 不存在")
        return
    
    # 加载已有标题
    existing_titles, _ = load_existing_titles(master_file)
    
    # 获取所有Excel文件
    excel_files = glob.glob(os.path.join(source_dir, "*.xlsx"))
    excel_files.sort()  # 按文件名排序
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    if not excel_files:
        print("错误: 未找到任何Excel文件")
        return
    
    # 统计信息
    total_remaining = 0
    file_stats = []
    
    # 处理每个文件
    for i, file_path in enumerate(excel_files, 1):
        print(f"\n{'='*50}")
        print(f"处理第 {i}/{len(excel_files)} 个文件")
        
        # 处理文件
        remaining_count, new_titles = process_single_file(file_path, existing_titles, output_dir)
        
        # 更新统计
        total_remaining += remaining_count
        file_stats.append({
            'filename': os.path.basename(file_path),
            'remaining_count': remaining_count
        })
        
        # 更新主文件和已有标题集合
        if new_titles:
            update_master_file(master_file, new_titles)
            existing_titles.update(new_titles)
    
    # 输出最终统计
    print(f"\n{'='*60}")
    print("处理完成！最终统计结果:")
    print(f"{'='*60}")
    
    for stat in file_stats:
        print(f"{stat['filename']}: {stat['remaining_count']} 条数据")
    
    print(f"{'='*60}")
    print(f"总计保留数据: {total_remaining} 条")
    print(f"处理文件数量: {len(excel_files)} 个")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main()
