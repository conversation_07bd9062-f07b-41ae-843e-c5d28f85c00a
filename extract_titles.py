#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取10万条title数据并生成Excel文件
从export10万条-20250624目录中的所有Excel文件中提取标题数据
"""

import pandas as pd
import os
import glob
from datetime import datetime

def extract_titles_from_excel(file_path):
    """从单个Excel文件中提取标题数据"""
    try:
        print(f"正在处理文件: {os.path.basename(file_path)}")
        
        # 以第2行作为列标题读取数据
        df = pd.read_excel(file_path, header=1)
        
        # 检查是否存在标题列
        if '标题' not in df.columns:
            print(f"警告: 文件 {file_path} 中未找到'标题'列")
            return []
        
        # 提取标题列并去除空值
        titles = df['标题'].dropna().tolist()
        print(f"从文件中提取到 {len(titles)} 个标题")
        
        return titles
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return []

def main():
    """主函数"""
    print("开始提取10万条title数据...")
    
    # 设置目录路径
    source_dir = "export10万条-20250624"
    
    # 检查目录是否存在
    if not os.path.exists(source_dir):
        print(f"错误: 目录 {source_dir} 不存在")
        return
    
    # 获取所有Excel文件
    excel_files = glob.glob(os.path.join(source_dir, "*.xlsx"))
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    if not excel_files:
        print("错误: 未找到任何Excel文件")
        return
    
    # 存储所有标题
    all_titles = []
    
    # 处理每个Excel文件
    for file_path in excel_files:
        titles = extract_titles_from_excel(file_path)
        all_titles.extend(titles)
    
    print(f"\n总共提取到 {len(all_titles)} 个标题")
    
    if not all_titles:
        print("错误: 未提取到任何标题数据")
        return
    
    # 创建DataFrame
    df_output = pd.DataFrame({
        '序号': range(1, len(all_titles) + 1),
        '标题': all_titles
    })
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"合并标题数据_{timestamp}.xlsx"
    
    # 保存到Excel文件
    try:
        df_output.to_excel(output_filename, index=False, engine='openpyxl')
        print(f"\n成功生成Excel文件: {output_filename}")
        print(f"文件包含 {len(all_titles)} 条标题数据")
        
        # 显示前10个标题作为预览
        print("\n前10个标题预览:")
        for i, title in enumerate(all_titles[:10], 1):
            print(f"{i:2d}. {title}")
        
        if len(all_titles) > 10:
            print("...")
            print(f"还有 {len(all_titles) - 10} 条标题数据")
            
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
